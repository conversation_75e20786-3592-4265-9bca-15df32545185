"""
Discovery agent for finding new tokens from multiple data sources.
Uses modern async patterns and intelligent caching.
"""

import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Any

import aiohttp
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from ..core.config import config

logger = structlog.get_logger(__name__)


class DiscoveryAgent:
    """
    Agent responsible for discovering new tokens from multiple sources.

    Data Sources:
    - DeFiLlama new listings
    - DEXScreener trending
    - CoinMarketCap new listings
    - Custom DEX monitoring
    """

    def __init__(self, db_manager, cache_manager, metrics_collector, coordinator):
        self.db_manager = db_manager
        self.cache_manager = cache_manager
        self.metrics_collector = metrics_collector
        self.coordinator = coordinator

        # HTTP session for API calls
        self.session: aiohttp.ClientSession | None = None

        # Tracking
        self.seen_tokens = set()
        self.last_discovery_time = None

        # Rate limiting
        self.request_semaphore = asyncio.Semaphore(
            config.system.max_concurrent_requests
        )

    async def initialize(self) -> None:
        """Initialize the discovery agent."""
        try:
            # Create HTTP session with optimized settings
            timeout = aiohttp.ClientTimeout(
                total=config.api.request_timeout, connect=config.api.connection_timeout
            )

            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=20,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )

            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    "User-Agent": f"{config.system.app_name}/{config.system.app_version}",
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip, deflate",
                },
            )

            # Load seen tokens from cache
            seen_tokens_data = await self.cache_manager.get("seen_tokens", "discovery")
            if seen_tokens_data:
                self.seen_tokens = set(seen_tokens_data)

            logger.info("Discovery agent initialized")

        except Exception as e:
            logger.error("Failed to initialize discovery agent", error=str(e))
            raise

    async def shutdown(self) -> None:
        """Shutdown the discovery agent."""
        try:
            # Save seen tokens to cache
            await self.cache_manager.set(
                "seen_tokens",
                list(self.seen_tokens),
                "discovery",
                ttl=86400,  # 24 hours
            )

            # Close HTTP session
            if self.session:
                await self.session.close()

            logger.info("Discovery agent shutdown complete")

        except Exception as e:
            logger.error("Error during discovery agent shutdown", error=str(e))

    async def health_check(self) -> bool:
        """Perform health check."""
        try:
            if not self.session or self.session.closed:
                return False

            # Test a simple request
            async with self.request_semaphore:
                async with self.session.get(
                    "https://httpbin.org/status/200"
                ) as response:
                    return response.status == 200

        except Exception as e:
            logger.error("Discovery agent health check failed", error=str(e))
            return False

    # ==================== MAIN DISCOVERY METHODS ====================

    async def discover_tokens(
        self, sources: list[str], min_age_hours: int = 24, limit: int = 50
    ) -> dict[str, Any]:
        """
        Discover new tokens from specified sources.

        Args:
            sources: List of data sources to use
            min_age_hours: Minimum token age in hours
            limit: Maximum number of tokens to return

        Returns:
            Dict containing discovered tokens and metadata
        """
        start_time = datetime.utcnow()
        discovered_tokens = []
        source_results = {}
        errors = []

        try:
            logger.info(
                "Starting token discovery",
                sources=sources,
                min_age_hours=min_age_hours,
                limit=limit,
            )

            # Run discovery from all sources concurrently
            tasks = []
            for source in sources:
                if hasattr(self, f"_discover_from_{source}"):
                    method = getattr(self, f"_discover_from_{source}")
                    tasks.append(
                        self._run_discovery_source(source, method, min_age_hours, limit)
                    )
                else:
                    logger.warning(f"Unknown discovery source: {source}")

            # Wait for all sources to complete
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                for i, result in enumerate(results):
                    source = sources[i] if i < len(sources) else f"unknown_{i}"

                    if isinstance(result, Exception):
                        error_msg = f"Source {source} failed: {str(result)}"
                        errors.append(error_msg)
                        logger.error(error_msg, source=source, error=str(result))
                    else:
                        source_results[source] = result
                        if result.get("tokens"):
                            discovered_tokens.extend(result["tokens"])

            # Remove duplicates and filter
            unique_tokens = self._deduplicate_tokens(discovered_tokens)
            filtered_tokens = await self._filter_tokens(unique_tokens, min_age_hours)

            # Limit results
            final_tokens = filtered_tokens[:limit]

            # Update tracking
            for token in final_tokens:
                token_key = f"{token['address']}_{token['chain']}"
                self.seen_tokens.add(token_key)

            # Cache results
            await self.cache_manager.set(
                "last_discovery_result",
                {
                    "tokens": final_tokens,
                    "source_results": source_results,
                    "timestamp": start_time.isoformat(),
                },
                "discovery",
                ttl=300,  # 5 minutes
            )

            duration = (datetime.utcnow() - start_time).total_seconds()

            logger.info(
                "Token discovery completed",
                tokens_found=len(final_tokens),
                sources_used=len(source_results),
                errors_count=len(errors),
                duration_seconds=duration,
            )

            return {
                "tokens": final_tokens,
                "source_results": source_results,
                "errors": errors,
                "duration_seconds": duration,
                "discovered_at": start_time.isoformat(),
            }

        except Exception as e:
            logger.error("Token discovery failed", error=str(e))
            raise

    async def _run_discovery_source(
        self, source: str, method, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Run discovery for a specific source with error handling."""
        try:
            result = await method(min_age_hours, limit)

            await self.metrics_collector.record_discovery_source(
                source=source, tokens_found=len(result.get("tokens", [])), success=True
            )

            return result

        except Exception as e:
            await self.metrics_collector.record_discovery_source(
                source=source, tokens_found=0, success=False, error=str(e)
            )
            raise

    # ==================== DATA SOURCE IMPLEMENTATIONS ====================

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_defillama(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DeFiLlama."""
        try:
            async with self.request_semaphore:
                # Get recent protocols
                url = "https://api.llama.fi/protocols"
                async with self.session.get(url) as response:
                    response.raise_for_status()
                    protocols = await response.json()

                tokens = []
                datetime.utcnow() - timedelta(hours=min_age_hours)

                for protocol in protocols[: limit * 2]:  # Get extra for filtering
                    # Basic token info (DeFiLlama doesn't provide direct token discovery)
                    # This is a simplified implementation
                    if "symbol" in protocol and "name" in protocol:
                        token = {
                            "symbol": protocol["symbol"],
                            "name": protocol["name"],
                            "address": protocol.get("address", ""),
                            "chain": protocol.get("chain", "ethereum").lower(),
                            "price_usd": 0.0,  # Would need additional API call
                            "market_cap_usd": protocol.get("tvl", 0),
                            "volume_24h_usd": 0.0,
                            "source": "defillama",
                            "discovered_at": datetime.utcnow(),
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)

                return {
                    "tokens": tokens[:limit],
                    "source": "defillama",
                    "total_checked": len(protocols),
                }

        except Exception as e:
            logger.error("DeFiLlama discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_dexscreener(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DEXScreener."""
        try:
            async with self.request_semaphore:
                # Get trending tokens
                url = "https://api.dexscreener.io/latest/dex/tokens/trending"
                async with self.session.get(url) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []

                for pair in data.get("pairs", [])[:limit]:
                    base_token = pair.get("baseToken", {})

                    if base_token.get("address"):
                        token = {
                            "symbol": base_token.get("symbol", ""),
                            "name": base_token.get("name", ""),
                            "address": base_token["address"],
                            "chain": pair.get("chainId", "ethereum").lower(),
                            "price_usd": float(pair.get("priceUsd", 0)),
                            "market_cap_usd": float(pair.get("marketCap", 0)),
                            "volume_24h_usd": float(
                                pair.get("volume", {}).get("h24", 0)
                            ),
                            "liquidity_usd": float(
                                pair.get("liquidity", {}).get("usd", 0)
                            ),
                            "source": "dexscreener",
                            "discovered_at": datetime.utcnow(),
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)

                return {
                    "tokens": tokens,
                    "source": "dexscreener",
                    "total_checked": len(data.get("pairs", [])),
                }

        except Exception as e:
            logger.error("DEXScreener discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_coinmarketcap(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from CoinMarketCap."""
        try:
            async with self.request_semaphore:
                # Note: This would require CMC API key in production
                # Using public endpoint with limitations
                url = "https://api.coinmarketcap.com/data-api/v3/cryptocurrency/listing"
                params = {
                    "start": 1,
                    "limit": limit,
                    "sortBy": "date_added",
                    "sortType": "desc",
                    "convert": "USD",
                    "cryptoType": "all",
                    "tagType": "all",
                }

                async with self.session.get(url, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []

                for item in data.get("data", {}).get("cryptoCurrencyList", []):
                    token_data = {
                        "symbol": item.get("symbol", ""),
                        "name": item.get("name", ""),
                        "address": "",  # CMC doesn't provide contract addresses in this endpoint
                        "chain": "ethereum",  # Default assumption
                        "price_usd": float(item.get("quotes", [{}])[0].get("price", 0)),
                        "market_cap_usd": float(
                            item.get("quotes", [{}])[0].get("marketCap", 0)
                        ),
                        "volume_24h_usd": float(
                            item.get("quotes", [{}])[0].get("volume24h", 0)
                        ),
                        "source": "coinmarketcap",
                        "discovered_at": datetime.utcnow(),
                    }

                    tokens.append(token_data)

                return {
                    "tokens": tokens,
                    "source": "coinmarketcap",
                    "total_checked": len(
                        data.get("data", {}).get("cryptoCurrencyList", [])
                    ),
                }

        except Exception as e:
            logger.error("CoinMarketCap discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_dextools(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from DexTools."""
        try:
            async with self.request_semaphore:
                # Get trending tokens from DexTools
                url = "https://www.dextools.io/shared/data/trending"
                headers = {
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                    "Accept": "application/json",
                }

                async with self.session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []
                current_time = datetime.now(datetime.timezone.utc)
                min_created_at = current_time - timedelta(hours=min_age_hours)

                for token_data in data.get("data", [])[:limit * 2]:  # Get more to filter
                    if token_data.get("address") and token_data.get("chain"):
                        # Parse creation time if available
                        created_at = None
                        if token_data.get("creationTime"):
                            try:
                                created_at = datetime.fromtimestamp(token_data["creationTime"] / 1000)
                            except (ValueError, TypeError):
                                pass

                        # Apply age filter if creation time is available
                        if created_at and created_at < min_created_at:
                            continue

                        token = {
                            "symbol": token_data.get("symbol", ""),
                            "name": token_data.get("name", ""),
                            "address": token_data["address"],
                            "chain": token_data["chain"].lower(),
                            "price_usd": float(token_data.get("price", 0)),
                            "market_cap_usd": float(token_data.get("mcap", 0)),
                            "volume_24h_usd": float(token_data.get("volume24h", 0)),
                            "liquidity_usd": float(token_data.get("liquidity", 0)),
                            "source": "dextools",
                            "discovered_at": current_time,
                            "created_at": created_at,
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)
                            self.seen_tokens.add(token_key)

                return {
                    "tokens": tokens[:limit],
                    "source": "dextools",
                    "total_checked": len(data.get("data", [])),
                }

        except Exception as e:
            logger.error("DexTools discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_coingecko(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover trending tokens from CoinGecko."""
        try:
            async with self.request_semaphore:
                # Get trending tokens from CoinGecko
                url = "https://api.coingecko.com/api/v3/search/trending"
                headers = {
                    "Accept": "application/json",
                    "User-Agent": "TokenAnalyzer/1.0"
                }

                async with self.session.get(url, headers=headers) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []
                current_time = datetime.now(datetime.timezone.utc)

                # Get trending coins
                for coin_data in data.get("coins", [])[:limit]:
                    coin_id = coin_data.get("item", {}).get("id")
                    if not coin_id:
                        continue

                    # Get detailed coin info
                    detail_url = f"https://api.coingecko.com/api/v3/coins/{coin_id}"
                    await asyncio.sleep(0.1)  # Rate limiting

                    async with self.session.get(detail_url, headers=headers) as detail_response:
                        if detail_response.status == 200:
                            detail_data = await detail_response.json()

                            # Extract contract addresses
                            platforms = detail_data.get("platforms", {})
                            for platform, address in platforms.items():
                                if address and platform in ["ethereum", "polygon-pos", "binance-smart-chain"]:
                                    token = {
                                        "symbol": detail_data.get("symbol", "").upper(),
                                        "name": detail_data.get("name", ""),
                                        "address": address,
                                        "chain": platform.replace("-pos", "").replace("binance-smart-chain", "bsc"),
                                        "price_usd": float(detail_data.get("market_data", {}).get("current_price", {}).get("usd", 0)),
                                        "market_cap_usd": float(detail_data.get("market_data", {}).get("market_cap", {}).get("usd", 0)),
                                        "volume_24h_usd": float(detail_data.get("market_data", {}).get("total_volume", {}).get("usd", 0)),
                                        "source": "coingecko",
                                        "discovered_at": current_time,
                                    }

                                    # Skip if we've seen this token
                                    token_key = f"{token['address']}_{token['chain']}"
                                    if token_key not in self.seen_tokens:
                                        tokens.append(token)
                                        self.seen_tokens.add(token_key)

                return {
                    "tokens": tokens[:limit],
                    "source": "coingecko",
                    "total_checked": len(data.get("coins", [])),
                }

        except Exception as e:
            logger.error("CoinGecko discovery failed", error=str(e))
            raise

    @retry(
        wait=wait_exponential(multiplier=1, min=2, max=10), stop=stop_after_attempt(3)
    )
    async def _discover_from_birdeye(
        self, min_age_hours: int, limit: int
    ) -> dict[str, Any]:
        """Discover tokens from Birdeye."""
        try:
            async with self.request_semaphore:
                # Get trending tokens from Birdeye (Solana focused)
                url = "https://public-api.birdeye.so/defi/tokenlist"
                headers = {
                    "Accept": "application/json",
                    "User-Agent": "TokenAnalyzer/1.0"
                }
                params = {
                    "sort_by": "v24hUSD",
                    "sort_type": "desc",
                    "offset": 0,
                    "limit": limit * 2  # Get more to filter
                }

                async with self.session.get(url, headers=headers, params=params) as response:
                    response.raise_for_status()
                    data = await response.json()

                tokens = []
                current_time = datetime.now(datetime.timezone.utc)
                min_created_at = current_time - timedelta(hours=min_age_hours)

                for token_data in data.get("data", {}).get("tokens", []):
                    if token_data.get("address"):
                        # Parse creation time if available
                        created_at = None
                        if token_data.get("createdTime"):
                            try:
                                created_at = datetime.fromtimestamp(token_data["createdTime"] / 1000, tz=datetime.timezone.utc)
                            except (ValueError, TypeError):
                                pass

                        # Apply age filter if creation time is available
                        if created_at and created_at < min_created_at:
                            continue

                        token = {
                            "symbol": token_data.get("symbol", ""),
                            "name": token_data.get("name", ""),
                            "address": token_data["address"],
                            "chain": "solana",  # Birdeye is primarily Solana
                            "price_usd": float(token_data.get("price", 0)),
                            "market_cap_usd": float(token_data.get("mc", 0)),
                            "volume_24h_usd": float(token_data.get("v24hUSD", 0)),
                            "liquidity_usd": float(token_data.get("liquidity", 0)),
                            "source": "birdeye",
                            "discovered_at": current_time,
                            "created_at": created_at,
                        }

                        # Skip if we've seen this token
                        token_key = f"{token['address']}_{token['chain']}"
                        if token_key not in self.seen_tokens:
                            tokens.append(token)
                            self.seen_tokens.add(token_key)

                return {
                    "tokens": tokens[:limit],
                    "source": "birdeye",
                    "total_checked": len(data.get("data", {}).get("tokens", [])),
                }

        except Exception as e:
            logger.error("Birdeye discovery failed", error=str(e))
            raise

    async def discover_trending_tokens(
        self, limit: int = 10, min_volume: int = 1000000, chains: list[str] = None
    ) -> dict[str, Any]:
        """
        Discover trending tokens (wrapper for discover_tokens with trending sources).

        Args:
            limit: Maximum number of tokens to return
            min_volume: Minimum 24h volume in USD
            chains: List of blockchain networks to search

        Returns:
            Dict containing discovered trending tokens and metadata
        """
        # Use trending-focused sources with new multi-source integration
        trending_sources = ["coingecko", "dexscreener", "dextools", "birdeye", "coinmarketcap"]

        # Filter sources based on chains if specified
        if chains:
            # Map chains to appropriate sources
            chain_sources = []
            if "ethereum" in chains:
                chain_sources.extend(["coingecko", "dexscreener", "dextools"])
            if "polygon" in chains or "bsc" in chains:
                chain_sources.extend(["dexscreener", "dextools"])
            if "solana" in chains:
                chain_sources.extend(["birdeye"])

            trending_sources = (
                list(set(trending_sources) & set(chain_sources))
                if chain_sources
                else trending_sources
            )

        # Discover tokens using the main discovery method
        result = await self.discover_tokens(
            sources=trending_sources,
            min_age_hours=1,  # Very recent for trending
            limit=limit,
        )

        # Filter by minimum volume if specified
        if min_volume > 0 and "tokens" in result:
            filtered_tokens = []
            for token in result["tokens"]:
                volume_24h = token.get("volume_24h", 0)
                if volume_24h >= min_volume:
                    filtered_tokens.append(token)

            result["tokens"] = filtered_tokens[:limit]
            result["filtered_by_volume"] = min_volume

        return result

    # ==================== UTILITY METHODS ====================

    def _deduplicate_tokens(self, tokens: list[dict[str, Any]]) -> list[dict[str, Any]]:
        """Remove duplicate tokens based on address and chain."""
        seen = set()
        unique_tokens = []

        for token in tokens:
            # Create a unique key
            if token.get("address"):
                key = f"{token['address']}_{token['chain']}"
            else:
                # For tokens without address, use symbol + name + chain
                key = f"{token.get('symbol', '')}_{token.get('name', '')}_{token['chain']}"

            key_hash = hashlib.md5(key.encode()).hexdigest()

            if key_hash not in seen:
                seen.add(key_hash)
                unique_tokens.append(token)

        return unique_tokens

    async def _filter_tokens(
        self, tokens: list[dict[str, Any]], min_age_hours: int
    ) -> list[dict[str, Any]]:
        """Filter tokens based on age and other criteria."""
        filtered_tokens = []
        min_created_at = datetime.now(datetime.timezone.utc) - timedelta(hours=min_age_hours)

        for token in tokens:
            # Basic filtering
            if not token.get("symbol") or not token.get("address"):
                continue

            # Skip tokens that are too new (if we have age data)
            if "age_days" in token and token["age_days"] is not None:
                if token["age_days"] * 24 < min_age_hours:
                    continue

            # Skip tokens with very low liquidity (if available)
            if "liquidity_usd" in token and token["liquidity_usd"] < 10000:
                continue

            filtered_tokens.append(token)

        return filtered_tokens

    async def get_metrics(self) -> dict[str, Any]:
        """Get discovery agent metrics."""
        return {
            "seen_tokens_count": len(self.seen_tokens),
            "last_discovery_time": self.last_discovery_time.isoformat()
            if self.last_discovery_time
            else None,
            "session_open": self.session is not None and not self.session.closed,
        }
